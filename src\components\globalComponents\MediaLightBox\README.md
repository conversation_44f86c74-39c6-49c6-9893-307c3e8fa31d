# MediaLightbox Component Suite

A comprehensive, reusable lightbox component suite for displaying images and videos with advanced features including thumbnail navigation, global configuration presets, and optimized video handling.

## 🚀 Key Features

### ✨ Enhanced User Experience
- **Blurred Background**: Consistent glassmorphism effect for all media types (including videos)
- **Optimized Video Thumbnails**: Uses video files directly as thumbnails with automatic frame preview
- **Smooth Animations**: Hardware-accelerated transitions and interactions
- **Mobile Responsive**: Adaptive design for all screen sizes

### 🎛️ Global Configuration System
- **Predefined Presets**: `default`, `compact`, and `gallery` configurations
- **No Code Duplication**: Reuse configurations across different instances
- **Easy Customization**: Override any setting while maintaining consistency

### 🎥 Advanced Video Support
- **Direct Video Thumbnails**: No need for separate thumbnail images
- **Transparent Background**: Videos maintain the blurred background effect
- **Auto Frame Selection**: Automatically seeks to a meaningful frame for thumbnails

## 📦 Components

### MediaLightbox
Main lightbox component with full-screen media display and thumbnail navigation.

### MediaThumbnail
Standalone thumbnail component with automatic video detection and play button overlay.

## 🔧 Installation & Usage

### Basic Usage

```tsx
import { MediaLightbox, createSlidesFromUrls } from '@/components/globalComponents/MediaLightBox';

const [lightboxOpen, setLightboxOpen] = useState(false);
const [lightboxIndex, setLightboxIndex] = useState(0);

const mediaUrls = ['image1.jpg', 'image2.jpg', 'video.mp4'];
const slides = createSlidesFromUrls(mediaUrls);

<MediaLightbox
  open={lightboxOpen}
  onClose={() => setLightboxOpen(false)}
  slides={slides}
  index={lightboxIndex}
  onSlideChange={setLightboxIndex}
  configType="default" // Use global configuration
/>
```

### Global Configuration Types

```tsx
// Default configuration - balanced for most use cases
<MediaLightbox configType="default" {...props} />

// Compact configuration - smaller thumbnails, mobile-optimized
<MediaLightbox configType="compact" {...props} />

// Gallery configuration - larger thumbnails, enhanced for image galleries
<MediaLightbox configType="gallery" {...props} />
```

### Custom Configuration

```tsx
// Override specific settings while using global config
<MediaLightbox
  configType="default"
  thumbnailConfig={{
    width: 120,
    height: 80,
  }}
  customStyles={{
    container: {
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
    },
  }}
  {...props}
/>
```

## 🎨 Configuration Options

### Available Config Types

| Type | Description | Best For |
|------|-------------|----------|
| `default` | Balanced settings for general use | Most applications |
| `compact` | Smaller thumbnails, reduced spacing | Mobile views, limited space |
| `gallery` | Larger thumbnails, enhanced visuals | Image-heavy content, portfolios |

### Thumbnail Configuration

```typescript
thumbnailConfig: {
  position: 'top' | 'bottom' | 'start' | 'end';
  width: number;
  height: number;
  border: number;
  borderRadius: number;
  padding: number;
  gap: number;
  showToggle: boolean;
}
```

### Video Configuration

```typescript
videoConfig: {
  controls: boolean;
  playsInline: boolean;
  preload: 'none' | 'metadata' | 'auto';
}
```

## 🛠️ Utility Functions

### createSlidesFromUrls
```tsx
const slides = createSlidesFromUrls(['img1.jpg', 'video.mp4'], 'Media Item');
```

### createSlidesFromRecommendation
```tsx
const media = {
  large: 'main-image.jpg',
  small: ['thumb1.jpg', 'thumb2.jpg', 'video.mp4']
};
const slides = createSlidesFromRecommendation(media);
```

### createSlidesFromUrlsWithAlts
```tsx
const items = [
  { url: 'image.jpg', alt: 'Description', type: 'image' },
  { url: 'video.mp4', alt: 'Video Description', type: 'video' }
];
const slides = createSlidesFromUrlsWithAlts(items);
```

## 🎯 Advanced Features

### Video Thumbnail Optimization
- Videos automatically use their own file as thumbnail source
- Seeks to 1-second mark for better frame preview
- No need for separate thumbnail images
- Maintains aspect ratio and quality

### Background Consistency
- All media types (images and videos) maintain the blurred background
- Videos have transparent backgrounds to show the glassmorphism effect
- Consistent visual experience across all content types

### Performance Optimizations
- Hardware-accelerated animations
- Lazy loading for thumbnails
- Optimized video preloading
- Smooth scrolling for thumbnail navigation

## 📱 Responsive Design

The component automatically adapts to different screen sizes:

- **Mobile**: Compact thumbnails, touch-optimized controls
- **Tablet**: Medium-sized thumbnails, balanced layout
- **Desktop**: Full-featured experience with larger thumbnails

## ♿ Accessibility

- Full keyboard navigation support
- Screen reader compatible
- Focus management
- ARIA labels and roles
- High contrast support

## 🔍 Examples

See `MediaLightboxExample.tsx` for comprehensive usage examples including:
- Simple image galleries
- Mixed image and video content
- Recommendation data structures
- Custom configuration options

## 🚀 Migration from Previous Version

If you're upgrading from a previous version:

1. Replace individual configuration objects with `configType` prop
2. Remove duplicate configuration code
3. Use utility functions for slide creation
4. Update video thumbnail handling (no separate images needed)

### Before
```tsx
<MediaLightbox
  thumbnailConfig={{ width: 100, height: 70, ... }}
  videoConfig={{ controls: true, ... }}
  carouselConfig={{ spacing: '20px', ... }}
  // ... many more config options
/>
```

### After
```tsx
<MediaLightbox
  configType="default"
  // Only override what you need to change
  thumbnailConfig={{ width: 120 }}
/>
```

## 🤝 Contributing

When adding new features or configurations:
1. Update the global configuration types
2. Add examples to the example file
3. Update this documentation
4. Ensure backward compatibility
