import { useRef } from 'react';
import Lightbox from 'yet-another-react-lightbox';
import Thumbnails from 'yet-another-react-lightbox/plugins/thumbnails';
import Video from 'yet-another-react-lightbox/plugins/video';
import 'yet-another-react-lightbox/styles.css';
import 'yet-another-react-lightbox/plugins/thumbnails.css';
import './lightbox-custom.css';

export interface MediaSlide {
  src: string;
  alt: string;
  type?: 'image' | 'video';
  poster?: string;
  thumbnail?: string;
  width?: number;
  height?: number;
  sources?: Array<{
    src: string;
    type: string;
  }>;
}

export interface MediaLightboxProps {
  /** Whether the lightbox is open */
  open: boolean;
  /** Function to close the lightbox */
  onClose: () => void;
  /** Array of media slides to display */
  slides: MediaSlide[];
  /** Current slide index */
  index: number;
  /** Function called when slide changes */
  onSlideChange?: (index: number) => void;
  /** Custom thumbnail configuration */
  thumbnailConfig?: {
    position?: 'top' | 'bottom' | 'start' | 'end';
    width?: number;
    height?: number;
    border?: number;
    borderRadius?: number;
    padding?: number;
    gap?: number;
    showToggle?: boolean;
  };
  /** Custom video configuration */
  videoConfig?: {
    controls?: boolean;
    playsInline?: boolean;
    preload?: 'none' | 'metadata' | 'auto';
  };
  /** Custom carousel configuration */
  carouselConfig?: {
    finite?: boolean;
    preload?: number;
    spacing?: `${number}px` | `${number}%` | number;
  };
  /** Custom animation configuration */
  animationConfig?: {
    fade?: number;
    swipe?: number;
    easing?: {
      fade?: string;
      swipe?: string;
    };
  };
  /** Custom controller configuration */
  controllerConfig?: {
    closeOnBackdropClick?: boolean;
    closeOnPullDown?: boolean;
    closeOnPullUp?: boolean;
  };
  /** Custom styles override */
  customStyles?: {
    container?: React.CSSProperties;
    thumbnailsContainer?: React.CSSProperties;
  };
}

/**
 * MediaLightbox - A reusable lightbox component for displaying images and videos
 * 
 * Features:
 * - Support for both images and videos
 * - Thumbnail navigation with customizable position and styling
 * - Blurred background with glassmorphism effect
 * - Smooth animations and transitions
 * - Fully configurable through props
 * - Accessibility support
 * - Mobile responsive
 */
const MediaLightbox = ({
  open,
  onClose,
  slides,
  index,
  onSlideChange,
  thumbnailConfig = {},
  videoConfig = {},
  carouselConfig = {},
  animationConfig = {},
  controllerConfig = {},
  customStyles = {}
}: MediaLightboxProps) => {
  const thumbnailsRef = useRef(null);

  // Helper function to detect if a URL is a video
  const isVideoUrl = (url: string): boolean => {
    const videoExtensions = ['.mp4', '.webm', '.ogg', '.mov', '.avi', '.mkv', '.m4v'];
    const videoHosts = ['gtv-videos-bucket', 'youtube.com', 'vimeo.com', 'sample-videos.com'];

    return videoExtensions.some(ext => url.toLowerCase().includes(ext.toLowerCase())) ||
           videoHosts.some(host => url.toLowerCase().includes(host));
  };

  // Process slides to ensure proper format for lightbox
  const processedSlides = slides.map((slide) => {
    if (slide.type === 'video' || isVideoUrl(slide.src)) {
      return {
        type: 'video' as const,
        src: slide.src,
        sources: slide.sources || [
          {
            src: slide.src,
            type: slide.src.toLowerCase().includes('.webm') ? 'video/webm' :
                  slide.src.toLowerCase().includes('.ogg') ? 'video/ogg' : 'video/mp4',
          },
        ],
        alt: slide.alt,
        poster: slide.poster || slide.src.replace(/\.(mp4|webm|ogg|mov|avi|mkv|m4v)$/i, '.jpg'),
        thumbnail: slide.thumbnail || slide.src,
        width: slide.width || 1920,
        height: slide.height || 1080,
      };
    }
    
    return {
      src: slide.src,
      alt: slide.alt,
      width: slide.width || 1920,
      height: slide.height || 1080,
    };
  });

  // Default configurations with user overrides
  const defaultThumbnailConfig = {
    position: 'bottom' as const,
    width: 100,
    height: 70,
    border: 2,
    borderRadius: 12,
    padding: 6,
    gap: 16,
    imageFit: 'cover' as const,
    vignette: true,
    showToggle: false,
    ...thumbnailConfig
  };

  const defaultVideoConfig = {
    controls: true,
    playsInline: true,
    preload: 'metadata' as const,
    ...videoConfig
  };

  const defaultCarouselConfig = {
    finite: false,
    preload: 2,
    spacing: '20px' as const,
    ...carouselConfig
  };

  const defaultAnimationConfig = {
    fade: 400,
    swipe: 600,
    easing: {
      fade: 'cubic-bezier(0.4, 0, 0.2, 1)',
      swipe: 'cubic-bezier(0.4, 0, 0.2, 1)',
    },
    ...animationConfig
  };

  const defaultControllerConfig = {
    closeOnBackdropClick: true,
    closeOnPullDown: true,
    closeOnPullUp: true,
    ...controllerConfig
  };

  const defaultStyles = {
    container: {
      backgroundColor: 'rgba(255, 255, 255, 0.15)',
      backdropFilter: 'blur(20px) saturate(180%)',
      ...customStyles.container
    },
    thumbnailsContainer: {
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      backdropFilter: 'blur(15px) saturate(150%)',
      ...customStyles.thumbnailsContainer
    }
  };

  return (
    <Lightbox
      open={open}
      close={onClose}
      slides={processedSlides}
      index={index}
      plugins={[Thumbnails, Video]}
      thumbnails={{
        ref: thumbnailsRef,
        ...defaultThumbnailConfig
      }}
      video={defaultVideoConfig}
      carousel={defaultCarouselConfig}
      animation={defaultAnimationConfig}
      controller={defaultControllerConfig}
      styles={defaultStyles}
      on={{
        view: ({ index }) => onSlideChange?.(index),
      }}
    />
  );
};

export default MediaLightbox;
