import { useRef } from 'react';
import Lightbox from 'yet-another-react-lightbox';
import Thumbnails from 'yet-another-react-lightbox/plugins/thumbnails';
import Video from 'yet-another-react-lightbox/plugins/video';
import 'yet-another-react-lightbox/styles.css';
import 'yet-another-react-lightbox/plugins/thumbnails.css';
import './lightbox-custom.css';
import { DEFAULT_MEDIA_LIGHTBOX_CONFIG, mergeMediaLightboxConfig, type MediaLightboxGlobalConfig } from './MediaLightboxConfig';

export interface MediaSlide {
  src: string;
  alt: string;
  type?: 'image' | 'video';
  poster?: string;
  thumbnail?: string;
  width?: number;
  height?: number;
  sources?: Array<{
    src: string;
    type: string;
  }>;
}

export interface MediaLightboxProps {
  /** Whether the lightbox is open */
  open: boolean;
  /** Function to close the lightbox */
  onClose: () => void;
  /** Array of media slides to display */
  slides: MediaSlide[];
  /** Current slide index */
  index: number;
  /** Function called when slide changes */
  onSlideChange?: (index: number) => void;
  /** Use a predefined global configuration */
  configType?: 'default' | 'compact' | 'gallery';
  /** Custom thumbnail configuration */
  thumbnailConfig?: {
    position?: 'top' | 'bottom' | 'start' | 'end';
    width?: number;
    height?: number;
    border?: number;
    borderRadius?: number;
    padding?: number;
    gap?: number;
    showToggle?: boolean;
  };
  /** Custom video configuration */
  videoConfig?: {
    controls?: boolean;
    playsInline?: boolean;
    preload?: 'none' | 'metadata' | 'auto';
  };
  /** Custom carousel configuration */
  carouselConfig?: {
    finite?: boolean;
    preload?: number;
    spacing?: `${number}px` | `${number}%` | number;
  };
  /** Custom animation configuration */
  animationConfig?: {
    fade?: number;
    swipe?: number;
    easing?: {
      fade?: string;
      swipe?: string;
    };
  };
  /** Custom controller configuration */
  controllerConfig?: {
    closeOnBackdropClick?: boolean;
    closeOnPullDown?: boolean;
    closeOnPullUp?: boolean;
  };
  /** Custom styles override */
  customStyles?: {
    container?: React.CSSProperties;
    thumbnailsContainer?: React.CSSProperties;
  };
}

/**
 * MediaLightbox - A reusable lightbox component for displaying images and videos
 * 
 * Features:
 * - Support for both images and videos
 * - Thumbnail navigation with customizable position and styling
 * - Blurred background with glassmorphism effect
 * - Smooth animations and transitions
 * - Fully configurable through props
 * - Accessibility support
 * - Mobile responsive
 */
const MediaLightbox = ({
  open,
  onClose,
  slides,
  index,
  onSlideChange,
  configType = 'default',
  thumbnailConfig = {},
  videoConfig = {},
  carouselConfig = {},
  animationConfig = {},
  controllerConfig = {},
  customStyles = {}
}: MediaLightboxProps) => {
  const thumbnailsRef = useRef(null);

  // Helper function to detect if a URL is a video
  const isVideoUrl = (url: string): boolean => {
    const videoExtensions = ['.mp4', '.webm', '.ogg', '.mov', '.avi', '.mkv', '.m4v'];
    const videoHosts = ['gtv-videos-bucket', 'youtube.com', 'vimeo.com', 'sample-videos.com'];

    return videoExtensions.some(ext => url.toLowerCase().includes(ext.toLowerCase())) ||
           videoHosts.some(host => url.toLowerCase().includes(host));
  };

  // Get global configuration based on configType
  const globalConfig = (() => {
    let baseConfig = DEFAULT_MEDIA_LIGHTBOX_CONFIG;

    if (configType === 'compact') {
      baseConfig = mergeMediaLightboxConfig(baseConfig, {
        thumbnailConfig: {
          position: 'bottom',
          width: 80,
          height: 60,
          border: 2,
          borderRadius: 8,
          padding: 4,
          gap: 12,
          showToggle: false,
        },
        customStyles: {
          container: {
            backgroundColor: 'rgba(255, 255, 255, 0.12)',
            backdropFilter: 'blur(15px) saturate(160%)',
          },
          thumbnailsContainer: {
            backgroundColor: 'rgba(255, 255, 255, 0.08)',
            backdropFilter: 'blur(12px) saturate(140%)',
          },
        },
      });
    } else if (configType === 'gallery') {
      baseConfig = mergeMediaLightboxConfig(baseConfig, {
        thumbnailConfig: {
          position: 'bottom',
          width: 120,
          height: 80,
          border: 3,
          borderRadius: 16,
          padding: 8,
          gap: 20,
          showToggle: true,
        },
        carouselConfig: {
          finite: false,
          preload: 3,
          spacing: '24px' as const,
        },
        animationConfig: {
          fade: 300,
          swipe: 500,
          easing: {
            fade: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
            swipe: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
          },
        },
      });
    }

    return baseConfig;
  })();

  // Process slides to ensure proper format for lightbox with optimized video thumbnails
  const processedSlides = slides.map((slide) => {
    const isVideo = slide.type === 'video' || isVideoUrl(slide.src);

    if (isVideo) {
      return {
        type: 'video' as const,
        src: slide.src,
        sources: slide.sources || [
          {
            src: slide.src,
            type: slide.src.toLowerCase().includes('.webm') ? 'video/webm' :
                  slide.src.toLowerCase().includes('.ogg') ? 'video/ogg' : 'video/mp4',
          },
        ],
        alt: slide.alt,
        poster: slide.poster,
        // Use video file directly as thumbnail for better performance and simplicity
        thumbnail: slide.thumbnail || slide.src,
        width: slide.width || 1920,
        height: slide.height || 1080,
      };
    }

    return {
      src: slide.src,
      alt: slide.alt,
      thumbnail: slide.thumbnail || slide.src,
      width: slide.width || 1920,
      height: slide.height || 1080,
    };
  });

  // Merge global config with user overrides
  const defaultThumbnailConfig = {
    ...globalConfig.thumbnailConfig,
    imageFit: 'cover' as const,
    vignette: true,
    ...thumbnailConfig
  };

  const defaultVideoConfig = {
    ...globalConfig.videoConfig,
    ...videoConfig
  };

  const defaultCarouselConfig = {
    ...globalConfig.carouselConfig,
    ...carouselConfig
  };

  const defaultAnimationConfig = {
    ...globalConfig.animationConfig,
    easing: {
      ...globalConfig.animationConfig.easing,
      ...animationConfig?.easing
    },
    ...animationConfig
  };

  const defaultControllerConfig = {
    ...globalConfig.controllerConfig,
    ...controllerConfig
  };

  const defaultStyles = {
    container: {
      ...globalConfig.customStyles.container,
      ...customStyles.container
    },
    thumbnailsContainer: {
      ...globalConfig.customStyles.thumbnailsContainer,
      ...customStyles.thumbnailsContainer
    }
  };

  return (
    <Lightbox
      open={open}
      close={onClose}
      slides={processedSlides}
      index={index}
      plugins={[Thumbnails, Video]}
      thumbnails={{
        ref: thumbnailsRef,
        ...defaultThumbnailConfig
      }}
      video={defaultVideoConfig}
      carousel={defaultCarouselConfig}
      animation={defaultAnimationConfig}
      controller={defaultControllerConfig}
      styles={defaultStyles}
      on={{
        view: ({ index }) => onSlideChange?.(index),
      }}
    />
  );
};

export default MediaLightbox;
