import React, { useState } from 'react';
import { 
  MediaLightbox, 
  MediaThumbnail, 
  createSlidesFromUrls,
  createSlidesFromRecommendation,
  createSlidesFromUrlsWithAlts 
} from './index';

/**
 * Example usage of MediaLightbox and MediaThumbnail components
 * 
 * This file demonstrates various ways to use the reusable lightbox components:
 * 1. Basic usage with image URLs
 * 2. Mixed image and video content
 * 3. Using with recommendation data structure
 * 4. Custom configuration options
 */

const MediaLightboxExample = () => {
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [lightboxIndex, setLightboxIndex] = useState(0);

  // Example 1: Simple image gallery
  const imageUrls = [
    'https://picsum.photos/800/600?random=1',
    'https://picsum.photos/800/600?random=2',
    'https://picsum.photos/800/600?random=3',
  ];

  // Example 2: Mixed media with custom alt texts
  const mixedMedia = [
    { url: 'https://picsum.photos/800/600?random=4', alt: 'Beautiful Landscape', type: 'image' as const },
    { url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4', alt: 'Sample Video', type: 'video' as const },
    { url: 'https://picsum.photos/800/600?random=5', alt: 'City View', type: 'image' as const },
  ];

  // Example 3: Recommendation data structure (like in ExploreRecommendation)
  const recommendationMedia = {
    large: 'https://picsum.photos/800/600?random=6',
    small: [
      'https://picsum.photos/400/300?random=7',
      'https://picsum.photos/400/300?random=8',
      'https://picsum.photos/400/300?random=9',
      'https://sample-videos.com/zip/10/mp4/SampleVideo_640x360_1mb.mp4',
    ]
  };

  // Create slides using utility functions
  const simpleSlides = createSlidesFromUrls(imageUrls, 'Gallery Image');
  const mixedSlides = createSlidesFromUrlsWithAlts(mixedMedia);
  const recommendationSlides = createSlidesFromRecommendation(recommendationMedia);

  const handleThumbnailClick = (slideIndex: number) => {
    setLightboxIndex(slideIndex);
    setLightboxOpen(true);
  };

  return (
    <div className="p-8 space-y-8">
      <h1 className="text-3xl font-bold mb-8">MediaLightbox Examples</h1>

      {/* Example 1: Simple Image Gallery */}
      <section>
        <h2 className="text-2xl font-semibold mb-4">1. Simple Image Gallery</h2>
        <div className="grid grid-cols-3 gap-4 max-w-md">
          {imageUrls.map((url, index) => (
            <div 
              key={index}
              className="cursor-pointer"
              onClick={() => handleThumbnailClick(index)}
            >
              <MediaThumbnail
                src={url}
                alt={`Gallery Image ${index + 1}`}
                width={150}
                height={100}
                className="w-full h-24 object-cover rounded-lg hover:opacity-80 transition-opacity"
              />
            </div>
          ))}
        </div>
      </section>

      {/* Example 2: Mixed Media Gallery */}
      <section>
        <h2 className="text-2xl font-semibold mb-4">2. Mixed Media Gallery (Images + Videos)</h2>
        <div className="grid grid-cols-3 gap-4 max-w-md">
          {mixedMedia.map((item, index) => (
            <div 
              key={index}
              className="cursor-pointer"
              onClick={() => handleThumbnailClick(index)}
            >
              <MediaThumbnail
                src={item.url}
                alt={item.alt}
                width={150}
                height={100}
                className="w-full h-24 object-cover rounded-lg hover:opacity-80 transition-opacity"
                isVideo={item.type === 'video'}
              />
            </div>
          ))}
        </div>
      </section>

      {/* Example 3: Recommendation Style Layout */}
      <section>
        <h2 className="text-2xl font-semibold mb-4">3. Recommendation Style Layout</h2>
        <div className="flex flex-col gap-2 max-w-xs">
          {/* Large image */}
          <div 
            className="cursor-pointer"
            onClick={() => handleThumbnailClick(0)}
          >
            <MediaThumbnail
              src={recommendationMedia.large}
              alt="Main Image"
              width={300}
              height={200}
              className="w-full h-32 object-cover rounded-t-lg hover:opacity-90 transition-opacity"
            />
          </div>
          
          {/* Small images grid */}
          <div className="grid grid-cols-4 gap-1">
            {recommendationMedia.small.map((url, index) => (
              <div 
                key={index}
                className="cursor-pointer"
                onClick={() => handleThumbnailClick(index + 1)}
              >
                <MediaThumbnail
                  src={url}
                  alt={`Small Image ${index + 1}`}
                  width={70}
                  height={70}
                  className="w-full h-16 object-cover rounded hover:opacity-90 transition-opacity"
                />
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* MediaLightbox Component - Now using global configuration */}
      <MediaLightbox
        open={lightboxOpen}
        onClose={() => setLightboxOpen(false)}
        slides={recommendationSlides} // You can switch this to test different slide sets
        index={lightboxIndex}
        onSlideChange={setLightboxIndex}
        configType="compact" // Using compact configuration for this example
      />

      {/* Usage Instructions */}
      <section className="bg-gray-50 p-6 rounded-lg">
        <h3 className="text-xl font-semibold mb-3">How to Use</h3>
        <div className="space-y-2 text-sm">
          <p><strong>MediaThumbnail:</strong> Automatically detects and renders video thumbnails with play buttons</p>
          <p><strong>MediaLightbox:</strong> Full-featured lightbox with thumbnail navigation and video support</p>
          <p><strong>Utility Functions:</strong> Helper functions to create slides from various data structures</p>
          <p><strong>Customizable:</strong> All styling, behavior, and layout options can be configured via props</p>
        </div>
      </section>
    </div>
  );
};

export default MediaLightboxExample;
